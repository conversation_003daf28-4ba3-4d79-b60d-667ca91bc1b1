// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2025 Morpho Association
pragma solidity 0.8.28;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";

import {VaultV2} from "../src/VaultV2.sol";
import {IVaultV2} from "../src/interfaces/IVaultV2.sol";
import {MockERC20} from "./mocks/MockERC20.sol";
import {MockVic} from "./mocks/MockVic.sol";
import {ErrorsLib} from "../src/libraries/ErrorsLib.sol";
import "../src/libraries/ConstantsLib.sol";

/**
 * @title Management Fee Vulnerability POC
 * @notice Demonstrates the vulnerability in VaultV2's management fee calculation
 * @dev The issue: managementFeeAssets = (newTotalAssets * elapsed).mulDivDown(managementFee, WAD)
 *      This overstates fees by ~31.5M times because managementFee is already per-second but 
 *      the calculation doesn't account for this properly.
 */
contract ManagementFeeVulnerabilityPOC is Test {
    VaultV2 public vault;
    MockERC20 public asset;
    MockVic public vic;
    
    address public owner = makeAddr("owner");
    address public curator = makeAddr("curator");
    address public allocator = makeAddr("allocator");
    address public managementFeeRecipient = makeAddr("managementFeeRecipient");
    address public user = makeAddr("user");
    
    // Test constants
    uint256 public constant INITIAL_DEPOSIT = 1000e18; // 1000 tokens
    uint256 public constant SECONDS_PER_YEAR = 365 days;
    
    function setUp() public {
        // Deploy contracts
        asset = new MockERC20("Test Token", "TEST", 18);
        vault = new VaultV2(owner, address(asset));
        vic = new MockVic(address(asset), address(vault));
        
        // Setup roles
        vm.startPrank(owner);
        vault.setCurator(curator);
        vault.setAllocator(allocator);
        vault.setVic(address(vic));
        vm.stopPrank();
        
        // Setup management fee recipient
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setManagementFeeRecipient, (managementFeeRecipient)));
        vault.setManagementFeeRecipient(managementFeeRecipient);
        
        // Give user tokens and approve vault
        asset.mint(user, INITIAL_DEPOSIT * 10);
        vm.prank(user);
        asset.approve(address(vault), type(uint256).max);
    }
    
    /**
     * @notice Test 1: Demonstrate the vulnerability with a realistic 1% annual management fee
     * @dev Shows that even a small 1% annual fee becomes massive due to the calculation error
     */
    function testManagementFeeVulnerability_RealisticScenario() public {
        console.log("=== Management Fee Vulnerability POC - Realistic Scenario ===");
        
        // Set a reasonable 1% annual management fee (0.01e18 / 365 days)
        uint256 annualFeeRate = 0.01e18; // 1% per year
        uint256 managementFeePerSecond = annualFeeRate / SECONDS_PER_YEAR;
        
        console.log("Annual fee rate: 1%");
        console.log("Management fee per second:", managementFeePerSecond);
        console.log("MAX_MANAGEMENT_FEE:", MAX_MANAGEMENT_FEE);
        
        // Set the management fee
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setManagementFee, (managementFeePerSecond)));
        vault.setManagementFee(managementFeePerSecond);
        
        // User deposits
        vm.prank(user);
        vault.deposit(INITIAL_DEPOSIT, user);
        
        console.log("Initial deposit:", INITIAL_DEPOSIT);
        console.log("Initial total assets:", vault.totalAssets());
        
        // Simulate 1 day passing
        uint256 timeElapsed = 1 days;
        vm.warp(block.timestamp + timeElapsed);
        
        console.log("Time elapsed: 1 day (", timeElapsed, "seconds)");
        
        // Try to accrue interest - this should revert due to underflow
        vm.expectRevert();
        vault.accrueInterest();
        
        console.log("✓ accrueInterest() reverted as expected due to underflow");
        
        // Let's calculate what the fee would be with the buggy formula
        uint256 buggyFeeAssets = (INITIAL_DEPOSIT * timeElapsed * managementFeePerSecond) / WAD;
        console.log("Buggy fee calculation result:", buggyFeeAssets);
        console.log("This is", buggyFeeAssets * 100 / INITIAL_DEPOSIT, "% of total assets!");
        
        // What the fee SHOULD be (1% annual = ~0.0027% daily)
        uint256 correctFeeAssets = (INITIAL_DEPOSIT * managementFeePerSecond * timeElapsed) / WAD;
        console.log("Correct fee should be:", correctFeeAssets);
        console.log("Overstatement factor:", buggyFeeAssets / correctFeeAssets);
    }
    
    /**
     * @notice Test 2: Show the vulnerability with maximum allowed management fee
     * @dev Demonstrates that even the maximum fee causes immediate failure
     */
    function testManagementFeeVulnerability_MaxFee() public {
        console.log("=== Management Fee Vulnerability POC - Maximum Fee ===");
        
        // Use maximum allowed management fee
        uint256 maxManagementFee = MAX_MANAGEMENT_FEE;
        
        console.log("Using MAX_MANAGEMENT_FEE:", maxManagementFee);
        console.log("This represents 5% annual fee");
        
        // Set the maximum management fee
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setManagementFee, (maxManagementFee)));
        vault.setManagementFee(maxManagementFee);
        
        // User deposits
        vm.prank(user);
        vault.deposit(INITIAL_DEPOSIT, user);
        
        console.log("Initial deposit:", INITIAL_DEPOSIT);
        
        // Even with just 1 second elapsed, the fee calculation will be massive
        vm.warp(block.timestamp + 1);
        
        console.log("Time elapsed: 1 second");
        
        // This will revert due to underflow
        vm.expectRevert();
        vault.accrueInterest();
        
        console.log("✓ accrueInterest() reverted after just 1 second!");
        
        // Calculate the buggy fee
        uint256 buggyFeeAssets = (INITIAL_DEPOSIT * 1 * maxManagementFee) / WAD;
        console.log("Buggy fee for 1 second:", buggyFeeAssets);
        console.log("This exceeds total assets by factor of:", buggyFeeAssets / INITIAL_DEPOSIT);
    }
    
    /**
     * @notice Test 3: Demonstrate that ANY non-zero management fee causes issues
     * @dev Shows the vulnerability affects any management fee setting
     */
    function testManagementFeeVulnerability_MinimalFee() public {
        console.log("=== Management Fee Vulnerability POC - Minimal Fee ===");

        // Use a very small management fee (0.01% annual)
        uint256 minimalAnnualFee = 0.0001e18; // 0.01% per year
        uint256 minimalFeePerSecond = minimalAnnualFee / SECONDS_PER_YEAR;

        console.log("Minimal annual fee: 0.01%");
        console.log("Minimal fee per second:", minimalFeePerSecond);

        // Set the minimal management fee
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setManagementFee, (minimalFeePerSecond)));
        vault.setManagementFee(minimalFeePerSecond);

        // User deposits
        vm.prank(user);
        vault.deposit(INITIAL_DEPOSIT, user);

        // Wait 1 hour
        uint256 timeElapsed = 1 hours;
        vm.warp(block.timestamp + timeElapsed);

        console.log("Time elapsed: 1 hour");

        // Even this minimal fee will cause issues
        vm.expectRevert();
        vault.accrueInterest();

        console.log("✓ Even 0.01% annual fee causes revert after 1 hour");

        // Show the calculation
        uint256 buggyFeeAssets = (INITIAL_DEPOSIT * timeElapsed * minimalFeePerSecond) / WAD;
        console.log("Buggy fee calculation:", buggyFeeAssets);
        console.log("As percentage of assets:", buggyFeeAssets * 100 / INITIAL_DEPOSIT, "%");
    }

    /**
     * @notice Test 4: Show impact on user operations
     * @dev Demonstrates that the vulnerability blocks all user interactions
     */
    function testManagementFeeVulnerability_BlocksUserOperations() public {
        console.log("=== Management Fee Vulnerability POC - Blocks User Operations ===");

        // Set a 2% annual management fee
        uint256 annualFeeRate = 0.02e18; // 2% per year
        uint256 managementFeePerSecond = annualFeeRate / SECONDS_PER_YEAR;

        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setManagementFee, (managementFeePerSecond)));
        vault.setManagementFee(managementFeePerSecond);

        // User deposits initially
        vm.prank(user);
        vault.deposit(INITIAL_DEPOSIT, user);

        console.log("Initial deposit successful:", INITIAL_DEPOSIT);

        // Wait some time
        vm.warp(block.timestamp + 6 hours);

        // Now all operations that trigger accrueInterest will fail
        console.log("After 6 hours, testing user operations...");

        // Test deposit
        vm.prank(user);
        vm.expectRevert();
        vault.deposit(100e18, user);
        console.log("✓ deposit() blocked");

        // Test mint
        vm.prank(user);
        vm.expectRevert();
        vault.mint(100e18, user);
        console.log("✓ mint() blocked");

        // Test withdraw
        vm.prank(user);
        vm.expectRevert();
        vault.withdraw(100e18, user, user);
        console.log("✓ withdraw() blocked");

        // Test redeem
        vm.prank(user);
        vm.expectRevert();
        vault.redeem(100e18, user, user);
        console.log("✓ redeem() blocked");

        // Test totalAssets view function
        vm.expectRevert();
        vault.totalAssets();
        console.log("✓ totalAssets() view blocked");

        console.log("All user operations are blocked due to management fee calculation bug!");
    }

    /**
     * @notice Test 5: Demonstrate the mathematical error
     * @dev Shows the exact calculation error and expected vs actual values
     */
    function testManagementFeeVulnerability_MathematicalAnalysis() public {
        console.log("=== Management Fee Vulnerability POC - Mathematical Analysis ===");

        uint256 totalAssets = 1000e18;
        uint256 timeElapsed = 1 days;
        uint256 annualFeeRate = 0.01e18; // 1% annual
        uint256 managementFeePerSecond = annualFeeRate / SECONDS_PER_YEAR;

        console.log("Total Assets:", totalAssets);
        console.log("Time Elapsed:", timeElapsed, "seconds (1 day)");
        console.log("Annual Fee Rate: 1%");
        console.log("Management Fee Per Second:", managementFeePerSecond);

        // Current buggy calculation: (totalAssets * elapsed) * managementFee / WAD
        uint256 buggyFeeAssets = (totalAssets * timeElapsed * managementFeePerSecond) / WAD;

        // Correct calculation: totalAssets * managementFee * elapsed / WAD
        // (which is the same as above, but managementFee should be annual rate / seconds per year)
        uint256 correctFeeAssets = (totalAssets * annualFeeRate * timeElapsed) / (SECONDS_PER_YEAR * WAD);

        console.log("Buggy calculation result:", buggyFeeAssets);
        console.log("Correct calculation result:", correctFeeAssets);
        console.log("Error factor:", buggyFeeAssets / correctFeeAssets);
        console.log("Buggy fee as % of assets:", (buggyFeeAssets * 100) / totalAssets, "%");
        console.log("Correct fee as % of assets:", (correctFeeAssets * 10000) / totalAssets, "basis points");

        // The buggy calculation makes the fee exceed total assets
        assertTrue(buggyFeeAssets > totalAssets, "Buggy fee exceeds total assets");
        assertTrue(correctFeeAssets < totalAssets / 1000, "Correct fee is reasonable");

        console.log("✓ Mathematical analysis confirms the vulnerability");
    }
}
